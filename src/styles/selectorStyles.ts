import { styled } from '@stitches/react';

export const SelectorContainer = styled('div', {
    position: 'relative',
    zIndex: 1000,
    pointerEvents: 'auto',
    padding: '10px',
    transition: 'all 0.2s ease',
    minWidth: '80px',
    width: 'auto',
    height: '100%',
    variants: {
        disabled: {
            true: {
                opacity: 0.5,
                cursor: 'not-allowed',
            }
        }
    },
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(9.5px)',
    WebkitBackdropFilter: 'blur(9.5px)',
    borderRadius: '10px',
    border: '1px solid rgba(255, 255, 255, 0.18)',
});

// 主要修改：调整Header容器为flex布局并居中内容
export const SelectorHeader = styled('div', {
    display: 'flex',
    alignItems: 'center',       // 确保内部元素垂直居中
    justifyContent: 'space-between',
    cursor: 'pointer',
    height: '100%',             // 继承父容器高度
});

// 主要修改：添加flex布局和自动外边距实现垂直居中
export const SelectorTitle = styled('div', {
    color: 'rgb(7, 81, 207)',
    fontSize: '17px',
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',       // 文本垂直居中
    lineHeight: '1',
    margin: 'auto 0',           // 垂直方向自动外边距居中
});

// 主要修改：使用flex布局确保图标垂直居中
export const SelectorDropdownIcon = styled('div', {
    color: 'rgb(7, 81, 207)',
    fontSize: '10px',
    transition: 'transform 0.2s ease',
    display: 'flex',
    alignItems: 'center',       // 图标垂直居中
    justifyContent: 'center',
    marginLeft: '8px',
    height: '100%',             // 继承父容器高度

    variants: {
        open: {
            true: {
                transform: 'rotate(180deg)',
            }
        }
    }
});

export const SelectorDropdown = styled('div', {
    position: 'absolute',
    top: '100%',
    left: 0,
    width: '100%',
    zIndex: 999,
    transition: 'all 0.2s ease',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: '4px',
    border: '1px dotted rgba(7, 81, 207, 0.3)',
    marginTop: '5px',
});

export const SelectorList = styled('div', {
    display: 'flex',
    flexDirection: 'column',
    maxHeight: '200px',
    overflowY: 'auto',
    gap: '2px',
    padding: '5px',

    '&::-webkit-scrollbar': {
        display: 'none',
    },
});

export const SelectorItem = styled('div', {
    padding: '6px 8px',
    fontSize: '14px',
    color: 'rgba(7, 81, 207)',
    cursor: 'pointer',
    borderRadius: '2px',
    transition: 'all 0.1s ease',
    display: 'flex',
    alignItems: 'center',       // 选项文本垂直居中
    lineHeight: '1.2',
    minHeight: '28px',          // 使用minHeight代替固定高度

    '&:hover': {
        backgroundColor: 'rgba(7, 81, 207, 0.15)',
        color: 'rgb(7, 81, 207)',
    },

    variants: {
        selected: {
            true: {
                backgroundColor: 'rgba(7, 81, 207, 0.2)',
                color: 'rgb(7, 81, 207)',
                fontWeight: 'bold',
            }
        }
    }
});