import { styled } from '@stitches/react';

// Define CSS custom properties for the glassmorphic design system
const glassmorphicVars = {
  '--glass-size': 'clamp(2rem, 4vw, 5rem)',
  '--glass-hover-time': '400ms',
  '--glass-hover-ease': 'cubic-bezier(0.25, 1, 0.5, 1)',
  '--glass-border-width': 'clamp(1px, 0.0625em, 4px)',
  '--glass-angle-1': '-75deg',
  '--glass-angle-2': '-45deg',
  '--glass-shadow-cutoff': '2em',
};

// Base glassmorphic container that provides the core design language
export const GlassmorphicBase = styled('div', {
  // CSS Custom Properties
  ...glassmorphicVars,

  // Core glassmorphic styling
  position: 'relative',
  borderRadius: '999vw',
  background: 'linear-gradient(-75deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05))',
  backdropFilter: 'blur(clamp(1px, 0.125em, 4px))',
  WebkitBackdropFilter: 'blur(clamp(1px, 0.125em, 4px))',

  // Box shadow for depth
  boxShadow: `
    inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
    inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
    0 0.25em 0.125em -0.125em rgba(0, 0, 0, 0.2),
    0 0 0.1em 0.25em inset rgba(255, 255, 255, 0.2),
    0 0 0 0 rgba(255, 255, 255, 1)
  `,

  // Transitions
  transition: 'all var(--glass-hover-time) var(--glass-hover-ease)',

  // Border gradient effect
  '&::before': {
    content: '',
    position: 'absolute',
    inset: 0,
    borderRadius: '999vw',
    padding: 'var(--glass-border-width)',
    background: `
      conic-gradient(
        from var(--glass-angle-1) at 50% 50%,
        rgba(0, 0, 0, 0.5),
        rgba(0, 0, 0, 0) 5% 40%,
        rgba(0, 0, 0, 0.5) 50%,
        rgba(0, 0, 0, 0) 60% 95%,
        rgba(0, 0, 0, 0.5)
      ),
      linear-gradient(180deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5))
    `,
    mask: 'linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0)',
    maskComposite: 'exclude',
    WebkitMask: 'linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0)',
    WebkitMaskComposite: 'exclude',
    transition: 'all var(--glass-hover-time) var(--glass-hover-ease)',
    boxShadow: 'inset 0 0 0 calc(var(--glass-border-width) / 2) rgba(255, 255, 255, 0.5)',
  },

  // Shine effect
  '&::after': {
    content: '',
    position: 'absolute',
    inset: 'calc(var(--glass-border-width) / 2)',
    borderRadius: '999vw',
    background: `
      linear-gradient(
        var(--glass-angle-2),
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 40% 50%,
        rgba(255, 255, 255, 0) 55%
      )
    `,
    backgroundSize: '200% 200%',
    backgroundPosition: '0% 50%',
    backgroundRepeat: 'no-repeat',
    mixBlendMode: 'screen',
    pointerEvents: 'none',
    transition: `
      background-position calc(var(--glass-hover-time) * 1.25) var(--glass-hover-ease),
      --glass-angle-2 calc(var(--glass-hover-time) * 1.25) var(--glass-hover-ease)
    `,
  },

  variants: {
    interactive: {
      true: {
        cursor: 'pointer',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        WebkitTapHighlightColor: 'rgba(0, 0, 0, 0)',

        '&:hover': {
          transform: 'scale(0.975)',
          backdropFilter: 'blur(0.01em)',
          WebkitBackdropFilter: 'blur(0.01em)',
          boxShadow: `
            inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
            inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
            0 0.15em 0.05em -0.1em rgba(0, 0, 0, 0.25),
            0 0 0.05em 0.1em inset rgba(255, 255, 255, 0.5)
          `,

          '&::before': {
            '--glass-angle-1': '-125deg',
          },

          '&::after': {
            backgroundPosition: '25% 50%',
          },
        },

        '&:active': {
          transform: 'scale(0.95) rotate3d(1, 0, 0, 25deg)',
          boxShadow: `
            inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
            inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
            0 0.125em 0.125em -0.125em rgba(0, 0, 0, 0.2),
            0 0 0.1em 0.25em inset rgba(255, 255, 255, 0.2),
            0 0.225em 0.05em 0 rgba(0, 0, 0, 0.05),
            0 0.25em 0 0 rgba(255, 255, 255, 0.75),
            inset 0 0.25em 0.05em 0 rgba(0, 0, 0, 0.15)
          `,

          '&::before': {
            '--glass-angle-1': '-75deg',
          },

          '&::after': {
            backgroundPosition: '50% 15%',
            '--glass-angle-2': '-15deg',
          },
        },
      },
    },

    size: {
      sm: {
        fontSize: 'clamp(0.75rem, 2vw, 1rem)',
        padding: '0.5em 1em',
      },
      md: {
        fontSize: 'clamp(1rem, 3vw, 1.25rem)',
        padding: '0.875em 1.5em',
      },
      lg: {
        fontSize: 'clamp(1.25rem, 4vw, 1.5rem)',
        padding: '1em 2em',
      },
    },

    variant: {
      primary: {
        background: 'linear-gradient(-75deg, rgba(7, 81, 207, 0.1), rgba(7, 81, 207, 0.3), rgba(7, 81, 207, 0.1))',
      },
      secondary: {
        background: 'linear-gradient(-75deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05))',
      },
      success: {
        background: 'linear-gradient(-75deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.3), rgba(34, 197, 94, 0.1))',
      },
      warning: {
        background: 'linear-gradient(-75deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.3), rgba(251, 191, 36, 0.1))',
      },
      danger: {
        background: 'linear-gradient(-75deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.3), rgba(239, 68, 68, 0.1))',
      },
    },

    shape: {
      rounded: {
        borderRadius: '0.5rem',
      },
      pill: {
        borderRadius: '999vw',
      },
      square: {
        borderRadius: '0.25rem',
      },
    },
  },

  defaultVariants: {
    size: 'md',
    variant: 'secondary',
    shape: 'pill',
  },

  // Touch device optimizations
  '@media (hover: none) and (pointer: coarse)': {
    '&::before, &:hover::before, &:active::before': {
      '--glass-angle-1': '-75deg',
    },
    '&::after, &:active::after': {
      '--glass-angle-2': '-45deg',
    },
  },
});

// Glassmorphic button component
export const GlassmorphicButton = styled('button', {
  // Inherit all base styles
  ...glassmorphicVars,

  // Reset button styles
  all: 'unset',
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  userSelect: 'none',
  WebkitUserSelect: 'none',
  WebkitTapHighlightColor: 'rgba(0, 0, 0, 0)',

  // Core glassmorphic styling
  position: 'relative',
  borderRadius: '999vw',
  background: 'linear-gradient(-75deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05))',
  backdropFilter: 'blur(clamp(1px, 0.125em, 4px))',
  WebkitBackdropFilter: 'blur(clamp(1px, 0.125em, 4px))',

  // Box shadow for depth
  boxShadow: `
    inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
    inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
    0 0.25em 0.125em -0.125em rgba(0, 0, 0, 0.2),
    0 0 0.1em 0.25em inset rgba(255, 255, 255, 0.2),
    0 0 0 0 rgba(255, 255, 255, 1)
  `,

  // Transitions
  transition: 'all var(--glass-hover-time) var(--glass-hover-ease)',

  // Text styling
  fontFamily: 'Inter, sans-serif',
  fontWeight: 500,
  letterSpacing: '-0.05em',
  color: 'rgba(50, 50, 50, 1)',
  textShadow: '0em 0.25em 0.05em rgba(0, 0, 0, 0.1)',
  WebkitFontSmoothing: 'antialiased',
  MozOsxFontSmoothing: 'grayscale',

  // Advanced pseudo-elements for border and shine effects
  '&::before': {
    content: '""',
    position: 'absolute',
    zIndex: 1,
    inset: 0,
    borderRadius: '999vw',
    width: 'calc(100% + var(--glass-border-width))',
    height: 'calc(100% + var(--glass-border-width))',
    top: 'calc(0% - var(--glass-border-width) / 2)',
    left: 'calc(0% - var(--glass-border-width) / 2)',
    padding: 'var(--glass-border-width)',
    boxSizing: 'border-box',
    background: `
      conic-gradient(
        from var(--glass-angle-1) at 50% 50%,
        rgba(0, 0, 0, 0.5),
        rgba(0, 0, 0, 0) 5% 40%,
        rgba(0, 0, 0, 0.5) 50%,
        rgba(0, 0, 0, 0) 60% 95%,
        rgba(0, 0, 0, 0.5)
      ),
      linear-gradient(180deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5))
    `,
    mask: 'linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0)',
    maskComposite: 'exclude',
    transition: 'all var(--glass-hover-time) var(--glass-hover-ease), --glass-angle-1 500ms ease',
    boxShadow: 'inset 0 0 0 calc(var(--glass-border-width) / 2) rgba(255, 255, 255, 0.5)',
  },

  '&::after': {
    content: '""',
    display: 'block',
    position: 'absolute',
    zIndex: 3,
    width: 'calc(100% - var(--glass-border-width))',
    height: 'calc(100% - var(--glass-border-width))',
    top: 'calc(0% + var(--glass-border-width) / 2)',
    left: 'calc(0% + var(--glass-border-width) / 2)',
    boxSizing: 'border-box',
    borderRadius: '999vw',
    overflow: 'clip',
    background: `
      linear-gradient(
        var(--glass-angle-2),
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 40% 50%,
        rgba(255, 255, 255, 0) 55%
      )
    `,
    mixBlendMode: 'screen',
    pointerEvents: 'none',
    backgroundSize: '200% 200%',
    backgroundPosition: '0% 50%',
    backgroundRepeat: 'no-repeat',
    transition: `
      background-position calc(var(--glass-hover-time) * 1.25) var(--glass-hover-ease),
      --glass-angle-2 calc(var(--glass-hover-time) * 1.25) var(--glass-hover-ease)
    `,
  },

  // Hover states
  '&:hover': {
    transform: 'scale(0.975)',
    backdropFilter: 'blur(0.01em)',
    WebkitBackdropFilter: 'blur(0.01em)',
    boxShadow: `
      inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
      inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
      0 0.15em 0.05em -0.1em rgba(0, 0, 0, 0.25),
      0 0 0.05em 0.1em inset rgba(255, 255, 255, 0.5),
      0 0 0 0 rgba(255, 255, 255, 1)
    `,
    textShadow: '0.025em 0.025em 0.025em rgba(0, 0, 0, 0.12)',

    '&::before': {
      '--glass-angle-1': '-125deg',
    },

    '&::after': {
      backgroundPosition: '25% 50%',
    },
  },

  // Active states
  '&:active': {
    transform: 'scale(0.95)',
    boxShadow: `
      inset 0 0.125em 0.125em rgba(0, 0, 0, 0.05),
      inset 0 -0.125em 0.125em rgba(255, 255, 255, 0.5),
      0 0.125em 0.125em -0.125em rgba(0, 0, 0, 0.2),
      0 0 0.1em 0.25em inset rgba(255, 255, 255, 0.2),
      0 0.225em 0.05em 0 rgba(0, 0, 0, 0.05),
      0 0.25em 0 0 rgba(255, 255, 255, 0.75),
      inset 0 0.25em 0.05em 0 rgba(0, 0, 0, 0.15)
    `,
    textShadow: '0.025em 0.25em 0.05em rgba(0, 0, 0, 0.12)',

    '&::before': {
      '--glass-angle-1': '-75deg',
    },

    '&::after': {
      backgroundPosition: '50% 15%',
      '--glass-angle-2': '-15deg',
    },
  },

  // Variants
  variants: {
    size: {
      sm: {
        fontSize: 'clamp(0.75rem, 2vw, 1rem)',
        padding: '0.5em 1em',
      },
      md: {
        fontSize: 'clamp(1rem, 3vw, 1.25rem)',
        padding: '0.875em 1.5em',
      },
      lg: {
        fontSize: 'clamp(1.25rem, 4vw, 1.5rem)',
        padding: '1em 2em',
      },
    },

    variant: {
      primary: {
        background: 'linear-gradient(-75deg, rgba(7, 81, 207, 0.1), rgba(7, 81, 207, 0.3), rgba(7, 81, 207, 0.1))',
      },
      secondary: {
        background: 'linear-gradient(-75deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05))',
      },
      success: {
        background: 'linear-gradient(-75deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.3), rgba(34, 197, 94, 0.1))',
      },
      warning: {
        background: 'linear-gradient(-75deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.3), rgba(251, 191, 36, 0.1))',
      },
      danger: {
        background: 'linear-gradient(-75deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.3), rgba(239, 68, 68, 0.1))',
      },
    },
  },

  defaultVariants: {
    size: 'md',
    variant: 'secondary',
  },

  // Touch device optimizations
  '@media (hover: none) and (pointer: coarse)': {
    '&::before, &:hover::before, &:active::before': {
      '--glass-angle-1': '-75deg',
    },
    '&::after, &:active::after': {
      '--glass-angle-2': '-45deg',
    },
  },
});

// Glassmorphic container for cards, panels, etc.
export const GlassmorphicCard = styled(GlassmorphicBase, {
  padding: '1.5rem',

  variants: {
    padding: {
      none: { padding: '0' },
      sm: { padding: '0.75rem' },
      md: { padding: '1.5rem' },
      lg: { padding: '2rem' },
      xl: { padding: '3rem' },
    },
  },

  defaultVariants: {
    padding: 'md',
  },
});

// Shadow container for enhanced depth
export const GlassmorphicShadow = styled('div', {
  position: 'absolute',
  width: 'calc(100% + var(--glass-shadow-cutoff))',
  height: 'calc(100% + var(--glass-shadow-cutoff))',
  top: 'calc(0% - var(--glass-shadow-cutoff) / 2)',
  left: 'calc(0% - var(--glass-shadow-cutoff) / 2)',
  filter: 'blur(clamp(2px, 0.125em, 12px))',
  WebkitFilter: 'blur(clamp(2px, 0.125em, 12px))',
  overflow: 'visible',
  pointerEvents: 'none',

  '&::after': {
    content: '""',
    position: 'absolute',
    zIndex: 0,
    inset: 0,
    borderRadius: '999vw',
    background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1))',
    width: 'calc(100% - var(--glass-shadow-cutoff) - 0.25em)',
    height: 'calc(100% - var(--glass-shadow-cutoff) - 0.25em)',
    top: 'calc(var(--glass-shadow-cutoff) - 0.5em)',
    left: 'calc(var(--glass-shadow-cutoff) - 0.875em)',
    padding: '0.125em',
    boxSizing: 'border-box',
    mask: 'linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0)',
    maskComposite: 'exclude',
    WebkitMask: 'linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0)',
    WebkitMaskComposite: 'exclude',
    transition: 'all var(--glass-hover-time) var(--glass-hover-ease)',
    opacity: 1,
  },
});

// Button wrapper with shadow for complete button experience
export const GlassmorphicButtonWrapper = styled('div', {
  ...glassmorphicVars,
  position: 'relative',
  zIndex: 2,
  borderRadius: '999vw',
  background: 'transparent',
  pointerEvents: 'none',
  transition: 'all var(--glass-hover-time) var(--glass-hover-ease)',

  // Enable pointer events for the button inside
  '& button': {
    pointerEvents: 'auto',
  },

  // Shadow hover effects
  '&:has(button:hover) .glassmorphic-shadow': {
    filter: 'blur(clamp(2px, 0.0625em, 6px))',
    WebkitFilter: 'blur(clamp(2px, 0.0625em, 6px))',

    '&::after': {
      top: 'calc(var(--glass-shadow-cutoff) - 0.875em)',
      opacity: 1,
    },
  },

  // Active rotation effect
  '&:has(button:active)': {
    transform: 'rotate3d(1, 0, 0, 25deg)',

    '& .glassmorphic-shadow': {
      filter: 'blur(clamp(2px, 0.125em, 12px))',
      WebkitFilter: 'blur(clamp(2px, 0.125em, 12px))',

      '&::after': {
        top: 'calc(var(--glass-shadow-cutoff) - 0.5em)',
        opacity: 0.75,
      },
    },
  },
});
